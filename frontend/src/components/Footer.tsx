import React, { useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Link
} from '@mui/material';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  useEffect(() => {
    // Check if Termly is loaded
    const checkTermly = () => {
      const termlyExists = typeof (window as any).termly !== 'undefined';
      if (!termlyExists) {
        console.warn('Termly script not loaded. Please check your Termly configuration.');
      }
    };

    // Check after a delay to allow script loading
    setTimeout(checkTermly, 2000);
  }, []);

  return (
    <Box
      component="footer"
      sx={{
        mt: 'auto',
        py: 3,
        backgroundColor: 'grey.50',
        borderTop: 1,
        borderColor: 'divider'
      }}
    >
      <Container maxWidth="lg">
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'space-between',
            alignItems: { xs: 'center', sm: 'flex-start' },
            gap: 2
          }}
        >
          {/* Left side - Copyright */}
          <Box sx={{ textAlign: { xs: 'center', sm: 'left' } }}>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ mb: 1 }}
            >
              © {currentYear} Gallery Tuner. All rights reserved.
            </Typography>
          </Box>

          {/* Right side - Links */}
          <Box
            sx={{
              display: 'flex',
              flexDirection: { xs: 'column', sm: 'row' },
              alignItems: 'center',
              gap: { xs: 1, sm: 3 },
              textAlign: 'center'
            }}
          >
            <button
              className="termly-display-preferences"
              type="button"
              onClick={() => {
                // Termly script should handle this automatically via the class name
                // If not working, check Termly dashboard configuration
                if (typeof (window as any).termly === 'undefined') {
                  console.warn('Termly script not loaded. Please check your Termly configuration.');
                }
              }}
              style={{
                color: 'inherit',
                textDecoration: 'none',
                fontSize: '0.875rem',
                cursor: 'pointer',
                border: 'none',
                background: 'none',
                padding: 0,
                fontFamily: 'inherit'
              }}
            >
              Consent Preferences
            </button>
            
            <Link
              href="/privacy-policy"
              sx={{
                color: 'text.secondary',
                textDecoration: 'none',
                fontSize: '0.875rem',
                '&:hover': {
                  color: 'primary.main',
                  textDecoration: 'underline'
                }
              }}
            >
              Privacy Policy
            </Link>
            
            <Link
              href="/terms-of-service"
              sx={{
                color: 'text.secondary',
                textDecoration: 'none',
                fontSize: '0.875rem',
                '&:hover': {
                  color: 'primary.main',
                  textDecoration: 'underline'
                }
              }}
            >
              Terms of Service
            </Link>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
