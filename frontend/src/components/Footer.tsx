import React from 'react';
import {
  Box,
  Container,
  Typography,
  Link
} from '@mui/material';

// Temporary debugging function
const debugTermly = () => {
  console.log('=== Termly Debug Info ===');
  console.log('window.termly exists:', typeof (window as any).termly !== 'undefined');
  console.log('window.termly object:', (window as any).termly);
  console.log('Termly buttons found:', document.querySelectorAll('.termly-display-preferences').length);
  console.log('All termly-related elements:', document.querySelectorAll('[class*="termly"]'));
  console.log('========================');
};

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  // Termly script is loaded in the head and will automatically handle the consent preferences button

  return (
    <Box
      component="footer"
      sx={{
        mt: 'auto',
        py: 3,
        backgroundColor: 'grey.50',
        borderTop: 1,
        borderColor: 'divider'
      }}
    >
      <Container maxWidth="lg">
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'space-between',
            alignItems: { xs: 'center', sm: 'flex-start' },
            gap: 2
          }}
        >
          {/* Left side - Copyright */}
          <Box sx={{ textAlign: { xs: 'center', sm: 'left' } }}>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ mb: 1 }}
            >
              © {currentYear} Gallery Tuner. All rights reserved.
            </Typography>
          </Box>

          {/* Right side - Links */}
          <Box
            sx={{
              display: 'flex',
              flexDirection: { xs: 'column', sm: 'row' },
              alignItems: 'center',
              gap: { xs: 1, sm: 3 },
              textAlign: 'center'
            }}
          >
          <button
            type="button"
            className="termly-display-preferences"
            style={{
              color: 'inherit',
              textDecoration: 'none',
              fontSize: '0.875rem',
              cursor: 'pointer',
              border: 'none',
              background: 'none',
              padding: 0,
              fontFamily: 'inherit'
            }}
          >
            Consent Preferences
          </button>

          {/* Temporary debug button */}
          <button
            type="button"
            onClick={debugTermly}
            style={{
              color: 'red',
              textDecoration: 'none',
              fontSize: '0.75rem',
              cursor: 'pointer',
              border: '1px solid red',
              background: 'none',
              padding: '2px 4px',
              fontFamily: 'inherit',
              marginLeft: '10px'
            }}
          >
            Debug Termly
          </button>
            
            <Link
              href="/privacy-policy"
              sx={{
                color: 'text.secondary',
                textDecoration: 'none',
                fontSize: '0.875rem',
                '&:hover': {
                  color: 'primary.main',
                  textDecoration: 'underline'
                }
              }}
            >
              Privacy Policy
            </Link>
            
            <Link
              href="/terms-of-service"
              sx={{
                color: 'text.secondary',
                textDecoration: 'none',
                fontSize: '0.875rem',
                '&:hover': {
                  color: 'primary.main',
                  textDecoration: 'underline'
                }
              }}
            >
              Terms of Service
            </Link>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
