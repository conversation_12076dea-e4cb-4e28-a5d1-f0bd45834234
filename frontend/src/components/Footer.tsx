import React, { useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Link,
  Divider
} from '@mui/material';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  useEffect(() => {
    // Check if Termly is loaded and log for debugging
    const checkTermly = () => {
      const termlyExists = typeof (window as any).termly !== 'undefined';
      const termlyButton = document.querySelector('.termly-display-preferences');
      const termlyScripts = Array.from(document.scripts).map(s => s.src).filter(src => src.includes('termly'));

      console.log('Termly script check:', {
        termlyExists,
        termlyButton,
        termlyScripts,
        windowTermly: (window as any).termly
      });

      // If Termly exists but button isn't working, try manual initialization
      if (termlyExists && termlyButton) {
        console.log('Termly detected, checking if button is properly initialized');
      }
    };

    // Check immediately
    checkTermly();

    // Also check after delays in case script is still loading
    setTimeout(checkTermly, 1000);
    setTimeout(checkTermly, 3000);
    setTimeout(checkTermly, 5000);
  }, []);

  return (
    <Box
      component="footer"
      sx={{
        mt: 'auto',
        py: 3,
        backgroundColor: 'grey.50',
        borderTop: 1,
        borderColor: 'divider'
      }}
    >
      <Container maxWidth="lg">
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'space-between',
            alignItems: { xs: 'center', sm: 'flex-start' },
            gap: 2
          }}
        >
          {/* Left side - Copyright */}
          <Box sx={{ textAlign: { xs: 'center', sm: 'left' } }}>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ mb: 1 }}
            >
              © {currentYear} Gallery Tuner. All rights reserved.
            </Typography>
          </Box>

          {/* Right side - Links */}
          <Box
            sx={{
              display: 'flex',
              flexDirection: { xs: 'column', sm: 'row' },
              alignItems: 'center',
              gap: { xs: 1, sm: 3 },
              textAlign: 'center'
            }}
          >
            <button
              className="termly-display-preferences"
              type="button"
              onClick={(e) => {
                console.log('Consent preferences clicked');
                // Let Termly handle this, but log for debugging
                if (typeof (window as any).termly !== 'undefined') {
                  console.log('Termly is available:', (window as any).termly);
                  // Try to manually trigger if available
                  if ((window as any).termly && (window as any).termly.open) {
                    console.log('Attempting to manually open Termly preferences');
                    (window as any).termly.open();
                  }
                } else {
                  console.log('Termly not found on window object');
                  console.log('Available window properties:', Object.keys(window).filter(key => key.toLowerCase().includes('termly')));
                }
              }}
              style={{
                color: 'inherit',
                textDecoration: 'none',
                fontSize: '0.875rem',
                cursor: 'pointer',
                border: 'none',
                background: 'none',
                padding: 0,
                fontFamily: 'inherit'
              }}
            >
              Consent Preferences
            </button>
            
            <Link
              href="/privacy-policy"
              sx={{
                color: 'text.secondary',
                textDecoration: 'none',
                fontSize: '0.875rem',
                '&:hover': {
                  color: 'primary.main',
                  textDecoration: 'underline'
                }
              }}
            >
              Privacy Policy
            </Link>
            
            <Link
              href="/terms-of-service"
              sx={{
                color: 'text.secondary',
                textDecoration: 'none',
                fontSize: '0.875rem',
                '&:hover': {
                  color: 'primary.main',
                  textDecoration: 'underline'
                }
              }}
            >
              Terms of Service
            </Link>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
