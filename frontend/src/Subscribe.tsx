import React from 'react';
import { Button } from '@mui/material';
import { Star } from '@mui/icons-material';
import { loadStripe } from '@stripe/stripe-js';

const stripePromise = loadStripe(process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY || '');

const Subscribe: React.FC = () => {
  const handleClick = async (event: React.MouseEvent<HTMLButtonElement>) => {
    const stripe = await stripePromise;

    const token = localStorage.getItem('jwt');
    const response = await fetch('/api/payments/create-checkout-session', {
      method: 'POST',
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    });

    const session = await response.json();

    if (stripe) {
      const result = await stripe.redirectToCheckout({
        sessionId: session.id,
      });

      if (result.error) {
        console.error(result.error.message);
      }
    }
  };

  return (
    <Button
      variant="contained"
      startIcon={<Star />}
      onClick={handleClick}
      sx={{
        backgroundColor: 'warning.main',
        color: 'warning.contrastText',
        textTransform: 'none',
        fontWeight: 500,
        boxShadow: 'none',
        '&:hover': {
          backgroundColor: 'warning.dark',
          boxShadow: '0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15)'
        },
        borderRadius: 1,
        px: 3,
        py: 1.5
      }}
    >
      Subscribe to Premium
    </Button>
  );
};

export default Subscribe;
