using Google.Cloud.Firestore;
using Microsoft.Extensions.Logging;
using VidCompressor.Data;
using VidCompressor.Models;
using VidCompressor.Models.Documents;

namespace VidCompressor.Repositories;

/// <summary>
/// Firestore implementation of CompressionJob repository
/// </summary>
public class CompressionJobRepository : ICompressionJobRepository
{
    private readonly FirestoreDbContext _context;
    private readonly ILogger<CompressionJobRepository> _logger;

    public CompressionJobRepository(FirestoreDbContext context, ILogger<CompressionJobRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<CompressionJob?> GetByIdAsync(string id)
    {
        try
        {
            var docRef = _context.CompressionJobs.Document(id);
            var snapshot = await docRef.GetSnapshotAsync();
            
            if (!snapshot.Exists)
            {
                return null;
            }

            var jobDoc = snapshot.ConvertTo<CompressionJobDocument>();
            return jobDoc.ToCompressionJob();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get compression job by ID: {JobId}", id);
            throw;
        }
    }

    public async Task<IEnumerable<CompressionJob>> GetAllAsync()
    {
        try
        {
            var snapshot = await _context.CompressionJobs.GetSnapshotAsync();
            return snapshot.Documents.Select(doc => doc.ConvertTo<CompressionJobDocument>().ToCompressionJob());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get all compression jobs");
            throw;
        }
    }

    public async Task<IEnumerable<CompressionJob>> GetByUserIdAsync(string userId)
    {
        try
        {
            var query = _context.CompressionJobs
                .WhereEqualTo("userId", userId)
                .WhereEqualTo("isCleared", false)
                .OrderByDescending("createdAt");
            
            var snapshot = await query.GetSnapshotAsync();
            return snapshot.Documents.Select(doc => doc.ConvertTo<CompressionJobDocument>().ToCompressionJob());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get compression jobs for user: {UserId}", userId);
            throw;
        }
    }

    public async Task<IEnumerable<CompressionJob>> GetByStatusAsync(CompressionJobStatus status)
    {
        try
        {
            var query = _context.CompressionJobs
                .WhereEqualTo("status", status.ToString())
                .OrderBy("createdAt");
            
            var snapshot = await query.GetSnapshotAsync();
            return snapshot.Documents.Select(doc => doc.ConvertTo<CompressionJobDocument>().ToCompressionJob());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get compression jobs by status: {Status}", status);
            throw;
        }
    }

    public async Task<IEnumerable<CompressionJob>> GetByUserAndMediaItemAsync(string userId, string mediaItemId)
    {
        try
        {
            var query = _context.CompressionJobs
                .WhereEqualTo("userId", userId)
                .WhereEqualTo("mediaItemId", mediaItemId)
                .OrderByDescending("createdAt");
            
            var snapshot = await query.GetSnapshotAsync();
            return snapshot.Documents.Select(doc => doc.ConvertTo<CompressionJobDocument>().ToCompressionJob());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get compression jobs for user {UserId} and media item {MediaItemId}", userId, mediaItemId);
            throw;
        }
    }

    public async Task<CompressionJob?> GetLatestByUserAndMediaItemAsync(string userId, string mediaItemId)
    {
        try
        {
            var query = _context.CompressionJobs
                .WhereEqualTo("userId", userId)
                .WhereEqualTo("mediaItemId", mediaItemId)
                .OrderByDescending("createdAt")
                .Limit(1);
            
            var snapshot = await query.GetSnapshotAsync();
            
            if (snapshot.Count == 0)
            {
                return null;
            }

            var jobDoc = snapshot.Documents.First().ConvertTo<CompressionJobDocument>();
            return jobDoc.ToCompressionJob();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get latest compression job for user {UserId} and media item {MediaItemId}", userId, mediaItemId);
            throw;
        }
    }

    public async Task<CompressionJob> AddAsync(CompressionJob entity)
    {
        try
        {
            var jobDoc = CompressionJobDocument.FromCompressionJob(entity);
            var docRef = _context.CompressionJobs.Document(entity.Id);
            await docRef.SetAsync(jobDoc);
            
            _logger.LogInformation("Added compression job: {JobId}", entity.Id);
            return entity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add compression job: {JobId}", entity.Id);
            throw;
        }
    }

    public async Task<CompressionJob> UpdateAsync(CompressionJob entity)
    {
        try
        {
            var jobDoc = CompressionJobDocument.FromCompressionJob(entity);
            var docRef = _context.CompressionJobs.Document(entity.Id);
            await docRef.SetAsync(jobDoc, SetOptions.MergeAll);
            
            _logger.LogInformation("Updated compression job: {JobId}", entity.Id);
            return entity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update compression job: {JobId}", entity.Id);
            throw;
        }
    }

    public async Task<bool> DeleteAsync(string id)
    {
        try
        {
            var docRef = _context.CompressionJobs.Document(id);
            var updates = new Dictionary<string, object>
            {
                { "isCleared", true },
                { "updatedAt", Timestamp.GetCurrentTimestamp() }
            };
            await docRef.UpdateAsync(updates);

            _logger.LogInformation("Soft-deleted compression job: {JobId}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to soft-delete compression job: {JobId}", id);
            return false;
        }
    }

    public async Task<bool> ExistsAsync(string id)
    {
        try
        {
            var docRef = _context.CompressionJobs.Document(id);
            var snapshot = await docRef.GetSnapshotAsync();
            return snapshot.Exists;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check if compression job exists: {JobId}", id);
            throw;
        }
    }

    public async Task<bool> UpdateStatusAsync(string jobId, CompressionJobStatus status, string? errorMessage = null)
    {
        try
        {
            var updates = new Dictionary<string, object>
            {
                { "status", status.ToString() },
                { "updatedAt", Timestamp.GetCurrentTimestamp() }
            };

            if (status == CompressionJobStatus.TranscodingInProgress && !string.IsNullOrEmpty(errorMessage))
            {
                updates["startedAt"] = Timestamp.GetCurrentTimestamp();
            }
            else if (status == CompressionJobStatus.Completed || status == CompressionJobStatus.Failed)
            {
                updates["completedAt"] = Timestamp.GetCurrentTimestamp();
            }

            if (!string.IsNullOrEmpty(errorMessage))
            {
                updates["errorMessage"] = errorMessage;
            }

            var docRef = _context.CompressionJobs.Document(jobId);
            await docRef.UpdateAsync(updates);
            
            _logger.LogInformation("Updated status for compression job {JobId} to {Status}", jobId, status);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update status for compression job: {JobId}", jobId);
            return false;
        }
    }

    public async Task<bool> UpdateCompletionAsync(string jobId, long? compressedSizeBytes, double? compressionRatio, string? outputPath)
    {
        try
        {
            var updates = new Dictionary<string, object>
            {
                { "updatedAt", Timestamp.GetCurrentTimestamp() }
            };

            if (compressedSizeBytes.HasValue)
            {
                updates["compressedSizeBytes"] = compressedSizeBytes.Value;
            }

            if (compressionRatio.HasValue)
            {
                updates["compressionRatio"] = compressionRatio.Value;
            }

            if (!string.IsNullOrEmpty(outputPath))
            {
                updates["outputStoragePath"] = outputPath;
            }

            var docRef = _context.CompressionJobs.Document(jobId);
            await docRef.UpdateAsync(updates);
            
            _logger.LogInformation("Updated completion details for compression job: {JobId}", jobId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update completion details for compression job: {JobId}", jobId);
            return false;
        }
    }

    public async Task<IEnumerable<CompressionJob>> GetByUserIdAsync(string userId, int limit)
    {
        try
        {
            var query = _context.CompressionJobs
                .WhereEqualTo("userId", userId)
                .WhereEqualTo("isCleared", false)
                .OrderByDescending("createdAt")
                .Limit(limit);

            var snapshot = await query.GetSnapshotAsync();
            return snapshot.Documents.Select(doc => doc.ConvertTo<CompressionJobDocument>().ToCompressionJob());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get compression jobs for user with limit: {UserId}, {Limit}", userId, limit);
            throw;
        }
    }

    public async Task<IEnumerable<CompressionJob>> GetDeletableJobsByUserIdAsync(string userId)
    {
        try
        {
            var query = _context.CompressionJobs
                .WhereEqualTo("userId", userId)
                .WhereEqualTo("isCleared", false)
                .WhereIn("status", new[] { "Completed", "Failed", "Cancelled" });

            var snapshot = await query.GetSnapshotAsync();
            return snapshot.Documents.Select(doc => doc.ConvertTo<CompressionJobDocument>().ToCompressionJob());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get deletable compression jobs for user: {UserId}", userId);
            throw;
        }
    }

    public async Task<bool> DeleteMultipleAsync(IEnumerable<string> jobIds)
    {
        try
        {
            var batch = _context.CompressionJobs.Database.StartBatch();
            var updates = new Dictionary<string, object>
            {
                { "isCleared", true },
                { "updatedAt", Timestamp.GetCurrentTimestamp() }
            };

            foreach (var jobId in jobIds)
            {
                var docRef = _context.CompressionJobs.Document(jobId);
                batch.Update(docRef, updates);
            }

            await batch.CommitAsync();

            _logger.LogInformation("Soft-deleted multiple compression jobs: {JobIds}", string.Join(", ", jobIds));
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to soft-delete multiple compression jobs: {JobIds}", string.Join(", ", jobIds));
            return false;
        }
    }

    public async Task<IEnumerable<CompressionJob>> GetOldJobsByUserIdAsync(string userId, DateTime cutoffDate)
    {
        try
        {
            var cutoffTimestamp = Timestamp.FromDateTime(cutoffDate.ToUniversalTime());

            var query = _context.CompressionJobs
                .WhereEqualTo("userId", userId)
                .WhereLessThan("completedAt", cutoffTimestamp);

            var snapshot = await query.GetSnapshotAsync();
            return snapshot.Documents.Select(doc => doc.ConvertTo<CompressionJobDocument>().ToCompressionJob())
                .Where(job => job.CompletedAt.HasValue &&
                             (!string.IsNullOrEmpty(job.CompressedFilePath) || !string.IsNullOrEmpty(job.OutputStoragePath)));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get old compression jobs for user: {UserId}, cutoff: {CutoffDate}", userId, cutoffDate);
            throw;
        }
    }
}
